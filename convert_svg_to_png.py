#!/usr/bin/env python3
"""
Convert SVG to PNG using svglib and reportlab
"""

try:
    from svglib.svglib import renderSVG
    from reportlab.graphics import renderPM
    from reportlab.lib.units import inch

    # Read and convert SVG to PNG
    drawing = renderSVG.renderSVG('competitive_advantage_framework.svg')

    # Render to PNG with high quality
    renderPM.drawToFile(drawing, 'competitive_advantage_framework.png', fmt='PNG', dpi=150)

    print("Successfully converted SVG to PNG!")
    print("Output file: competitive_advantage_framework.png")

except ImportError as e:
    print(f"Error: Required module not found: {e}")
    print("Please install with: pip install reportlab svglib")
except Exception as e:
    print(f"Error during conversion: {e}")

    # Fallback: Try using cairosvg if available
    try:
        import cairosvg
        cairosvg.svg2png(
            url='competitive_advantage_framework.svg',
            write_to='competitive_advantage_framework.png',
            output_width=1200,
            output_height=700
        )
        print("Fallback: Successfully converted using cairosvg!")
    except ImportError:
        print("Cairosvg also not available. Please install one of: cairosvg, svglib+reportlab")
    except Exception as e2:
        print(f"Fallback also failed: {e2}")
