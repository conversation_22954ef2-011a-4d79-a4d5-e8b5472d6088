<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Competitive Advantage Framework</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            max-width: 1240px;
            margin: 0 auto;
        }
        .svg-container {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .instructions {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #5a9bd4;
            max-width: 800px;
            text-align: left;
        }
        .instructions h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .instructions p {
            margin: 8px 0;
            color: #5a6c7d;
        }
        .instructions code {
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="svg-container">
            <object data="competitive_advantage_framework.svg" type="image/svg+xml" width="1200" height="700">
                <p>Your browser does not support SVG. Please use a modern browser to view this diagram.</p>
            </object>
        </div>
        
        <div class="instructions">
            <h3>如何保存为PNG图片：</h3>
            <p><strong>方法1 - 浏览器截图：</strong></p>
            <p>1. 右键点击上方的图表</p>
            <p>2. 选择"另存为图片"或"Save image as"</p>
            <p>3. 选择PNG格式保存</p>
            
            <p><strong>方法2 - 浏览器开发者工具：</strong></p>
            <p>1. 按 <code>F12</code> 打开开发者工具</p>
            <p>2. 按 <code>Ctrl+Shift+P</code> 打开命令面板</p>
            <p>3. 输入 "screenshot" 并选择 "Capture full size screenshot"</p>
            
            <p><strong>方法3 - 在线转换工具：</strong></p>
            <p>1. 访问 <code>https://convertio.co/svg-png/</code> 或类似网站</p>
            <p>2. 上传 <code>competitive_advantage_framework.svg</code> 文件</p>
            <p>3. 下载转换后的PNG文件</p>
        </div>
    </div>
</body>
</html>
