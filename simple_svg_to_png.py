#!/usr/bin/env python3
"""
Simple SVG to PNG converter using wand (ImageMagick Python binding)
"""

import os
import sys

def convert_with_wand():
    """Try converting with Wand (ImageMagick)"""
    try:
        from wand.image import Image
        from wand.color import Color
        
        with Image() as img:
            img.format = 'svg'
            with open('competitive_advantage_framework.svg', 'rb') as f:
                img.blob = f.read()
            img.format = 'png'
            img.background_color = Color('white')
            img.alpha_channel = 'remove'
            img.save(filename='competitive_advantage_framework.png')
        
        print("Successfully converted SVG to PNG using Wand!")
        return True
    except ImportError:
        print("Wand not available")
        return False
    except Exception as e:
        print(f"Wand conversion failed: {e}")
        return False

def convert_with_cairosvg():
    """Try converting with cairosvg"""
    try:
        import cairosvg
        cairosvg.svg2png(
            url='competitive_advantage_framework.svg',
            write_to='competitive_advantage_framework.png',
            output_width=1200,
            output_height=700
        )
        print("Successfully converted SVG to PNG using cairosvg!")
        return True
    except ImportError:
        print("cairosvg not available")
        return False
    except Exception as e:
        print(f"cairosvg conversion failed: {e}")
        return False

def convert_with_svglib():
    """Try converting with svglib"""
    try:
        from svglib.svglib import renderSVG
        from reportlab.graphics import renderPM
        
        drawing = renderSVG.renderSVG('competitive_advantage_framework.svg')
        renderPM.drawToFile(drawing, 'competitive_advantage_framework.png', fmt='PNG', dpi=150)
        print("Successfully converted SVG to PNG using svglib!")
        return True
    except ImportError:
        print("svglib/reportlab not available")
        return False
    except Exception as e:
        print(f"svglib conversion failed: {e}")
        return False

def main():
    if not os.path.exists('competitive_advantage_framework.svg'):
        print("Error: competitive_advantage_framework.svg not found!")
        return
    
    print("Attempting SVG to PNG conversion...")
    
    # Try different conversion methods
    methods = [convert_with_cairosvg, convert_with_svglib, convert_with_wand]
    
    for method in methods:
        if method():
            print(f"Output file: competitive_advantage_framework.png")
            return
    
    print("All conversion methods failed. Please install one of:")
    print("- pip install cairosvg")
    print("- pip install reportlab svglib")
    print("- pip install Wand (requires ImageMagick)")

if __name__ == "__main__":
    main()
