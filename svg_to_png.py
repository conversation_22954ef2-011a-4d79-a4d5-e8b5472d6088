#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SVG转PNG转换器
将SVG文件转换为高清PNG图片
"""

import cairosvg
from PIL import Image
import os
import sys

def convert_svg_to_png(svg_path, output_path=None, dpi=300, scale=1.0):
    """
    将SVG文件转换为高清PNG图片
    
    参数:
    svg_path: SVG文件路径
    output_path: 输出PNG文件路径（可选，默认为同名PNG文件）
    dpi: 分辨率，默认300 DPI（高清）
    scale: 缩放比例，默认1.0
    """
    
    # 检查SVG文件是否存在
    if not os.path.exists(svg_path):
        print(f"错误：SVG文件不存在: {svg_path}")
        return False
    
    # 生成输出文件路径
    if output_path is None:
        base_name = os.path.splitext(svg_path)[0]
        output_path = f"{base_name}_high_quality.png"
    
    try:
        print(f"正在转换: {svg_path}")
        print(f"输出文件: {output_path}")
        print(f"分辨率: {dpi} DPI")
        print(f"缩放比例: {scale}")
        
        # 使用cairosvg转换SVG到PNG
        cairosvg.svg2png(
            url=svg_path,
            write_to=output_path,
            dpi=dpi,
            scale=scale
        )
        
        # 检查生成的文件
        if os.path.exists(output_path):
            # 获取图片尺寸信息
            with Image.open(output_path) as img:
                width, height = img.size
                print(f"转换成功！")
                print(f"图片尺寸: {width} x {height} 像素")
                print(f"文件大小: {os.path.getsize(output_path) / 1024 / 1024:.2f} MB")
                return True
        else:
            print("转换失败：未生成输出文件")
            return False
            
    except Exception as e:
        print(f"转换过程中出现错误: {str(e)}")
        return False

def main():
    """主函数"""
    # 当前目录下的SVG文件
    svg_file = "dissertation_framework_diagram.svg"
    
    if not os.path.exists(svg_file):
        print(f"错误：找不到SVG文件: {svg_file}")
        print("请确保SVG文件在当前目录中")
        return
    
    print("=" * 50)
    print("SVG转PNG高清转换器")
    print("=" * 50)
    
    # 转换为不同质量的PNG
    qualities = [
        {"dpi": 300, "scale": 1.0, "suffix": "300dpi"},
        {"dpi": 600, "scale": 1.0, "suffix": "600dpi_ultra_high"},
        {"dpi": 150, "scale": 2.0, "suffix": "150dpi_2x_scale"}
    ]
    
    success_count = 0
    for quality in qualities:
        output_file = f"dissertation_framework_diagram_{quality['suffix']}.png"
        
        print(f"\n正在生成 {quality['suffix']} 版本...")
        if convert_svg_to_png(
            svg_file, 
            output_file, 
            dpi=quality['dpi'], 
            scale=quality['scale']
        ):
            success_count += 1
        print("-" * 30)
    
    print(f"\n转换完成！成功生成 {success_count}/{len(qualities)} 个文件")
    print("生成的文件:")
    for quality in qualities:
        output_file = f"dissertation_framework_diagram_{quality['suffix']}.png"
        if os.path.exists(output_file):
            print(f"  ✓ {output_file}")
        else:
            print(f"  ✗ {output_file} (失败)")

if __name__ == "__main__":
    main()
